import React, { useState } from "react";
import { Controller } from "react-hook-form";
import { Upload, X } from "lucide-react";
import PriorityDropdown from "../components/PriorityDropdown";
import LabelSelector from "../components/LabelSelector";
import Calendar from "../../Announcements/components/Calendar";
import TimePicker from "../components/TimePicker";
import TowerSelector from "../../Announcements/components/TowerSelector";
import UnitSelector from "../../Announcements/components/UnitSelector";
import MemberSelector from "../../Announcements/components/MemberSelector";
import GroupSelector from "../../Announcements/components/GroupSelector";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

/**
 * EditNoticeForm Component
 * Comprehensive form for editing notices with sectioned design and advanced functionality
 */
const EditNoticeForm = ({
  // Form props
  control,
  handleSubmit,
  watch,
  setValue,
  errors,
  isSubmitting,
  onSubmit,

  // State props
  currentUser,
  attachments,
  notice,
  formChanged,
  hasFormBeenModified,

  // Error states
  titleWordLimitError,
  fileUploadError,
  dateOrderError,

  // Handlers
  handleTitleChange,
  getTitleWordCount,
  handleFileUpload,
  removeAttachment,
  handleMemberSelect,
  handleGroupSelect,
  isFormValid,

  // Watched values
  watchedValues,
  selectedTowers
}) => {
  // Check if all required fields are filled
  const areFieldsValid = () => {
    const values = watch();
    const isValid = (
      values.title &&
      values.creatorName &&
      values.priority &&
      values.label &&
      values.startDate &&
      values.startTime &&
      values.endDate &&
      values.endTime &&
      values.selectedTowers?.length > 0 &&
      (values.postAs === 'Creator' ||
        (values.postAs === 'Group' && values.selectedGroupId) ||
        (values.postAs === 'Member' && values.selectedMemberId))
    );

    console.log("=== NOTICE FORM BUTTON STATE ===");
    console.log("hasFormBeenModified:", hasFormBeenModified);
    console.log("areFieldsValid:", isValid);
    console.log("Button should be enabled:", hasFormBeenModified && isValid);
    console.log("Field checks:");
    console.log("- title:", !!values.title);
    console.log("- creatorName:", !!values.creatorName);
    console.log("- priority:", !!values.priority);
    console.log("- label:", !!values.label);
    console.log("- startDate:", !!values.startDate);
    console.log("- startTime:", !!values.startTime);
    console.log("- endDate:", !!values.endDate);
    console.log("- endTime:", !!values.endTime);
    console.log("- selectedTowers:", values.selectedTowers?.length > 0);
    console.log("- postAs check:", (values.postAs === 'Creator' || (values.postAs === 'Group' && values.selectedGroupId) || (values.postAs === 'Member' && values.selectedMemberId)));

    return isValid;
  };
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);

  // Handle image preview
  const handleImagePreview = (attachment) => {
    setPreviewImage(attachment);
    setShowImagePreview(true);
  };

  // Close preview modal
  const closeImagePreview = () => {
    setShowImagePreview(false);
    setPreviewImage(null);
  };

  return (
    <div className="relative">
      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <LoadingAnimation />
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Notice Author Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">Notice Author</h3>

          {/* Creator Name and Post as on different rows */}
          <div className="space-y-4">
            {/* Creator Name */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Creator Name
              </label>
              <Controller
                name="creatorName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700 cursor-not-allowed"
                    value={currentUser?.full_name || currentUser?.fullName || 'Current User'}
                  />
                )}
              />
            </div>

            {/* Post as */}
            <div>
              <div className="flex items-center mb-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Post as <span className="text-primary">*</span>
                </label>
                <div className="ml-8">
                  <Controller
                    name="postAs"
                    control={control}
                    render={({ field }) => (
                      <div className="flex space-x-6">
                        <label className={`flex items-center ${notice?.post_as === 'creator' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                          <div className="relative">
                            <input
                              type="radio"
                              {...field}
                              value="Creator"
                              checked={field.value === 'Creator'}
                              onChange={(e) => {
                                if (notice?.post_as === 'creator') {
                                  field.onChange(e.target.value);
                                  // Clear member and group selections when switching to Creator
                                  setValue('selectedMemberId', '');
                                  setValue('selectedMemberName', '');
                                  setValue('selectedGroupId', '');
                                  setValue('selectedGroupName', '');
                                  // Set creator name to current user
                                  const user = currentUser || (() => {
                                    try {
                                      const member = localStorage.getItem('member');
                                      return member ? JSON.parse(member) : null;
                                    } catch (error) {
                                      return null;
                                    }
                                  })();
                                  if (user) {
                                    setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                  }
                                }
                              }}
                              disabled={notice?.post_as !== 'creator'}
                              className="sr-only peer"
                            />
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:border-4"></div>
                          </div>
                          <span className="ml-2 text-sm text-gray-700">Creator</span>
                        </label>
                        <label className={`flex items-center ${notice?.post_as === 'group' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                          <div className="relative">
                            <input
                              type="radio"
                              {...field}
                              value="Group"
                              checked={field.value === 'Group'}
                              onChange={(e) => {
                                if (notice?.post_as === 'group') {
                                  field.onChange(e.target.value);
                                  // Clear member selection when switching to Group
                                  setValue('selectedMemberId', '');
                                  setValue('selectedMemberName', '');
                                  // Clear group selection to allow fresh selection
                                  setValue('selectedGroupId', '');
                                  setValue('selectedGroupName', '');
                                  // Set creator name to current user when switching to Group
                                  const user = currentUser || (() => {
                                    try {
                                      const member = localStorage.getItem('member');
                                      return member ? JSON.parse(member) : null;
                                    } catch (error) {
                                      return null;
                                    }
                                  })();
                                  if (user) {
                                    setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                  }
                                }
                              }}
                              disabled={notice?.post_as !== 'group'}
                              className="sr-only peer"
                            />
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:border-4"></div>
                          </div>
                          <span className="ml-2 text-sm text-gray-700">Group</span>
                        </label>
                        <label className={`flex items-center ${notice?.post_as === 'member' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                          <div className="relative">
                            <input
                              type="radio"
                              {...field}
                              value="Member"
                              checked={field.value === 'Member'}
                              onChange={(e) => {
                                if (notice?.post_as === 'member') {
                                  field.onChange(e.target.value);
                                  // Clear member and group selections when switching to Member
                                  setValue('selectedMemberId', '');
                                  setValue('selectedMemberName', '');
                                  setValue('selectedGroupId', '');
                                  setValue('selectedGroupName', '');
                                  // Set creator name to current user when switching to Member (like Group)
                                  const user = currentUser || (() => {
                                    try {
                                      const member = localStorage.getItem('member');
                                      return member ? JSON.parse(member) : null;
                                    } catch (error) {
                                      return null;
                                    }
                                  })();
                                  if (user) {
                                    setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                  }
                                }
                              }}
                              disabled={notice?.post_as !== 'member'}
                              className="sr-only peer"
                            />
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:border-4"></div>
                          </div>
                          <span className="ml-2 text-sm text-gray-700">Member</span>
                        </label>
                      </div>
                    )}
                  />
                </div>
              </div>
              {errors.postAs && (
                <p className="mt-1 text-sm text-red-600">{errors.postAs.message}</p>
              )}
            </div>



            {/* Auto Name Field - Only show when Creator is selected */}
            {watchedValues.postAs === 'Creator' && (
              <div>
                <Controller
                  name="autoName"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 cursor-not-allowed"
                      placeholder="Auto-filled from Creator Name"
                    />
                  )}
                />
              </div>
            )}
          </div>
        </div>

        {/* Notice Information Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">Notice Information</h3>

          {/* Title */}
          <div className="mb-4">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Title <span className="text-primary">*</span>
            </label>
            <Controller
              name="title"
              control={control}
              render={({ field }) => (
                <div>
                  <input
                    {...field}
                    type="text"
                    onChange={(e) => handleTitleChange(e.target.value, field.onChange)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Notice Title (max 10 words)"
                    value={field.value}
                  />
                  <div className="flex justify-between items-center mt-1">
                    <div>
                      {errors.title && (
                        <ErrorMessage message={errors.title.message} />
                      )}
                      {titleWordLimitError && (
                        <ErrorMessage message={titleWordLimitError} />
                      )}
                    </div>
                    <p className={`text-xs ${getTitleWordCount(field.value) > 10 ? 'text-red-500' : 'text-gray-500'}`}>
                      {getTitleWordCount(field.value)}/10 words
                    </p>
                  </div>
                </div>
              )}
            />
          </div>

          {/* Description */}
          <div className="mb-4">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Description
            </label>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <div>
                  <textarea
                    {...field}
                    rows={4}
                    onChange={(e) => {
                      const inputValue = e.target.value;

                      // Only limit if we exceed 100 words
                      if (inputValue.trim() === "") {
                        field.onChange(inputValue);
                        return;
                      }

                      const words = inputValue.trim().split(/\s+/);
                      if (words.length <= 100) {
                        // Allow normal typing if within limit
                        field.onChange(inputValue);
                      } else {
                        // Only limit when exceeding 100 words
                        const limited = words.slice(0, 100).join(" ");
                        field.onChange(limited);
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Write your description here... (max 100 words)"
                    value={field.value}
                  />
                  <div className="flex justify-between items-center mt-1">
                    <div>
                      {errors.description && (
                        <ErrorMessage message={errors.description.message} />
                      )}
                    </div>
                    <p className={`text-xs ${getTitleWordCount(field.value) > 100 ? 'text-red-500' : 'text-gray-500'}`}>
                      {getTitleWordCount(field.value || '')}/100 words
                    </p>
                  </div>
                </div>
              )}
            />
          </div>

          {/* Images */}
          <div className="mb-4">
            <label className="block text-sm font-semibold text-gray-700 mb-2">
              Images <span className="text-primary">*</span>
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="edit-notice-file-upload"
              />
              <label
                htmlFor="edit-notice-file-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">Click to upload images</span>
                <span className="text-xs text-gray-400 mt-1">PNG, JPG, GIF up to 5MB each (max 10 images)</span>
              </label>
            </div>

            {/* Error Message */}
            <ErrorMessage message={fileUploadError} />

            {/* Display uploaded images */}
            {attachments.length > 0 && (
              <div className="mt-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    Uploaded Images ({attachments.length}/10)
                  </span>
                </div>
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="relative group">
                      <img
                        src={attachment.url || attachment.base64 || attachment.preview}
                        alt={attachment.name || attachment.file_name}
                        className="w-full h-20 object-cover rounded border cursor-pointer hover:opacity-75 transition-opacity"
                        onClick={() => handleImagePreview(attachment)}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                      {/* Fallback */}
                      <div className="hidden w-full h-20 bg-gray-100 rounded border items-center justify-center">
                        <span className="text-xs text-gray-500">Image</span>
                      </div>
                      
                      {/* Remove Button */}
                      <button
                        type="button"
                        onClick={() => removeAttachment(attachment.id)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-3 h-3" />
                      </button>
                      
                      {/* Existing File Indicator */}
                      {attachment.isExisting && (
                        <div className="absolute bottom-1 left-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded">
                          Existing
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Label and Priority Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Label */}
            <div>
              <Controller
                name="label"
                control={control}
                render={({ field }) => (
                  <LabelSelector
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.label?.message}
                  />
                )}
              />
            </div>

            {/* Priority */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Priority <span className="text-primary">*</span>
              </label>
              <Controller
                name="priority"
                control={control}
                render={({ field }) => (
                  <PriorityDropdown
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.priority?.message}
                  />
                )}
              />
            </div>
          </div>
        </div>

        {/* Notice Visibility Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">Notice Visibility</h3>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Start Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Date <span className="text-primary">*</span>
              </label>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <Calendar
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start date"
                  />
                )}
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
              )}
            </div>

            {/* Start Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Time <span className="text-primary">*</span>
              </label>
              <Controller
                name="startTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start time"
                  />
                )}
              />
              {errors.startTime && (
                <p className="mt-1 text-sm text-red-600">{errors.startTime.message}</p>
              )}
            </div>

            {/* End Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Date <span className="text-primary">*</span>
              </label>
              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <Calendar
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select end date"
                  />
                )}
              />
              {errors.endDate && (
                <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
              )}
            </div>

            {/* End Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Time <span className="text-primary">*</span>
              </label>
              <Controller
                name="endTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select end time"
                  />
                )}
              />
              {errors.endTime && (
                <p className="mt-1 text-sm text-red-600">{errors.endTime.message}</p>
              )}
            </div>
          </div>

          {/* Date/Time Validation Error */}
          {dateOrderError && (
            <ErrorMessage message={dateOrderError} />
          )}
        </div>

        {/* Tower and Unit Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Tower Selector */}
            <div>
              <Controller
                name="selectedTowers"
                control={control}
                render={({ field }) => (
                  <TowerSelector
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.selectedTowers?.message}
                  />
                )}
              />
            </div>

            {/* Unit Selector */}
            <div>
              <Controller
                name="selectedUnits"
                control={control}
                render={({ field }) => (
                  <UnitSelector
                    value={field.value}
                    onChange={field.onChange}
                    selectedTowers={selectedTowers}
                    error={errors.selectedUnits?.message}
                    isEditing={true}
                  />
                )}
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={!isFormValid() || isSubmitting}
            className={`w-full px-8 py-3 rounded-md transition duration-200 font-medium ${
              hasFormBeenModified && areFieldsValid() && !isSubmitting
                ? 'bg-primary text-white hover:bg-[#34877A] cursor-pointer'
                : 'bg-white text-primary border-2 border-primary cursor-not-allowed'
            } ${
              (!hasFormBeenModified || !areFieldsValid() || isSubmitting) ? "opacity-50" : ""
            }`}
          >
            {isSubmitting ? 'Updating...' : 'Send'}
          </button>
        </div>
      </form>

      {/* Image Preview Modal */}
      {showImagePreview && previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeImagePreview}
              className="absolute top-4 right-4 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-colors z-10"
            >
              <X className="w-4 h-4" />
            </button>
            <img
              src={previewImage.url || previewImage.base64 || previewImage.preview}
              alt={previewImage.name}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded">
              {previewImage.name}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditNoticeForm;
